package com.fasnote.alm.checklist.service;

import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;

import com.fasnote.alm.checklist.model.TemplateType;
import com.polarion.alm.tracker.ITrackerService;
import com.polarion.alm.tracker.model.ITypeOpt;
import com.polarion.platform.persistence.IEnumeration;

/**
 * 模板类型服务
 * 负责管理模板类型，支持本地存储和 Polarion 集成
 */
@Service
public class TemplateTypeService {
    
    // 本地存储的默认模板类型
    private static final List<TemplateType> DEFAULT_TEMPLATE_TYPES = Arrays.asList(
        new TemplateType("code-review", "代码评审", "代码质量和规范检查"),
        new TemplateType("design-review", "设计评审", "系统设计和架构评审"),
        new TemplateType("security-review", "安全评审", "安全漏洞和风险评估"),
        new TemplateType("performance-review", "性能评审", "系统性能和优化评审"),
        new TemplateType("test-review", "测试评审", "测试用例和测试策略评审"),
        new TemplateType("document-review", "文档评审", "技术文档和用户文档评审")
    );
    
    private final ITrackerService trackerService;
    
    public TemplateTypeService(ITrackerService trackerService) {
		super();
		this.trackerService = trackerService;
	}
    
    /**
     * 获取所有模板类型
     * 
     * @param projectId 项目ID
     * @return 模板类型列表
     * @throws IOException IO异常
     */
    public List<TemplateType> getAllTemplateTypes(String projectId) throws IOException {
        if (projectId == null || projectId.trim().isEmpty()) {
            throw new IllegalArgumentException("项目ID不能为空");
        }
            return getPolarionWorkItemTypes(projectId);
    }

	/**
     * 根据ID获取模板类型
     * 
     * @param projectId 项目ID
     * @param typeId 类型ID
     * @return 模板类型的Optional包装
     * @throws IOException IO异常
     */
    public Optional<TemplateType> getTemplateTypeById(String projectId, String typeId) throws IOException {
        if (projectId == null || projectId.trim().isEmpty()) {
            throw new IllegalArgumentException("项目ID不能为空");
        }
        if (typeId == null || typeId.trim().isEmpty()) {
            return Optional.empty();
        }
        
        List<TemplateType> allTypes = getAllTemplateTypes(projectId);
        return allTypes.stream()
                .filter(type -> typeId.equals(type.getId()))
                .findFirst();
    }
    
    /**
     * 根据名称获取模板类型
     * 
     * @param projectId 项目ID
     * @param typeName 类型名称
     * @return 模板类型的Optional包装
     * @throws IOException IO异常
     */
    public Optional<TemplateType> getTemplateTypeByName(String projectId, String typeName) throws IOException {
        if (projectId == null || projectId.trim().isEmpty()) {
            throw new IllegalArgumentException("项目ID不能为空");
        }
        if (typeName == null || typeName.trim().isEmpty()) {
            return Optional.empty();
        }
        
        List<TemplateType> allTypes = getAllTemplateTypes(projectId);
        return allTypes.stream()
                .filter(type -> typeName.equals(type.getName()))
                .findFirst();
    }
    
    /**
     * 从 Polarion 获取工作项类型
     * 
     * @param projectId 项目ID
     * @return 工作项类型列表
     * @throws IOException IO异常
     */
    private List<TemplateType> getPolarionWorkItemTypes(String projectId) throws IOException {
    	IEnumeration<ITypeOpt> workItemTypeEnum = trackerService.getTrackerProject(projectId).getWorkItemTypeEnum();
    	List<TemplateType> list = workItemTypeEnum.getAllOptions().stream().map(t->{
    		return new TemplateType(t.getId(), t.getName());
    	}).collect(Collectors.toList());
        return list;
    }
    
    /**
     * 验证模板类型是否有效
     * 
     * @param projectId 项目ID
     * @param templateType 模板类型
     * @return 是否有效
     * @throws IOException IO异常
     */
    public boolean isValidTemplateType(String projectId, TemplateType templateType) throws IOException {
        if (templateType == null || templateType.getId() == null || templateType.getName() == null) {
            return false;
        }
        
        Optional<TemplateType> existingType = getTemplateTypeById(projectId, templateType.getId());
        return existingType.isPresent();
    }
}
