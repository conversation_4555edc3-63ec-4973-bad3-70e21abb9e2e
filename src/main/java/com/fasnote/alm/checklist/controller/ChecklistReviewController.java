package com.fasnote.alm.checklist.controller;

import java.io.IOException;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.fasnote.alm.checklist.dto.ApiResponse;
import com.fasnote.alm.checklist.dto.ResponseBuilder;
import com.fasnote.alm.checklist.model.ReviewTemplateVersion;
import com.fasnote.alm.checklist.model.TemplateSnapshot;
import com.fasnote.alm.checklist.service.ChecklistReviewService;

/**
 * 检查单评审管理控制器
 * 提供评审管理的 REST API 接口
 */
@RestController
@RequestMapping("/reviews")
public class ChecklistReviewController {

    private final ChecklistReviewService reviewService;

    public ChecklistReviewController(ChecklistReviewService reviewService) {
        this.reviewService = reviewService;
    }

    /**
     * 获取检查单的模板版本信息
     * GET /api/reviews/{id}/template-version?projectId=xxx
     */
    @GetMapping("/{id}/template-version")
    public ResponseEntity<ApiResponse<ReviewTemplateVersion>> getReviewTemplateVersion(
            @PathVariable String id,
            @RequestParam String projectId) {
        try {
            // 参数验证
            if (id == null || id.trim().isEmpty()) {
                return ResponseBuilder.badRequest("评审ID不能为空");
            }
            if (projectId == null || projectId.trim().isEmpty()) {
                return ResponseBuilder.badRequest("项目ID不能为空");
            }

            ReviewTemplateVersion templateVersion = reviewService.getReviewTemplateVersion(projectId.trim(), id.trim());
            if (templateVersion != null) {
                return ResponseBuilder.success(templateVersion);
            } else {
                return ResponseBuilder.notFound("未找到模板版本信息");
            }
        } catch (IOException e) {
            return ResponseBuilder.error("TEMPLATE_VERSION_READ_ERROR", "读取模板版本信息失败: " + e.getMessage());
        } catch (Exception e) {
            return ResponseBuilder.error("UNKNOWN_ERROR", "获取模板版本信息时发生未知错误: " + e.getMessage());
        }
    }

    /**
     * 获取检查单的模板快照
     * GET /api/reviews/{id}/snapshots/{snapshotId}?projectId=xxx
     */
    @GetMapping("/{id}/snapshots/{snapshotId}")
    public ResponseEntity<ApiResponse<TemplateSnapshot>> getReviewSnapshot(
            @PathVariable String id,
            @PathVariable String snapshotId,
            @RequestParam String projectId) {
        try {
            // 参数验证
            if (id == null || id.trim().isEmpty()) {
                return ResponseBuilder.badRequest("评审ID不能为空");
            }
            if (snapshotId == null || snapshotId.trim().isEmpty()) {
                return ResponseBuilder.badRequest("快照ID不能为空");
            }
            if (projectId == null || projectId.trim().isEmpty()) {
                return ResponseBuilder.badRequest("项目ID不能为空");
            }

            TemplateSnapshot snapshot = reviewService.getReviewSnapshot(projectId.trim(), id.trim(), snapshotId.trim());
            if (snapshot != null) {
                return ResponseBuilder.success(snapshot);
            } else {
                return ResponseBuilder.notFound("未找到快照信息");
            }
        } catch (IOException e) {
            return ResponseBuilder.error("SNAPSHOT_READ_ERROR", "读取快照信息失败: " + e.getMessage());
        } catch (Exception e) {
            return ResponseBuilder.error("UNKNOWN_ERROR", "获取快照信息时发生未知错误: " + e.getMessage());
        }
    }
}